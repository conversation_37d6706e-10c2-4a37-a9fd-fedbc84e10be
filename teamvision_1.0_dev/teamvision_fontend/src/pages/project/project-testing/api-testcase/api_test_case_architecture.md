# API 测试用例功能后端开发设计文档

## 1. 项目概述

### 1.1 功能描述

基于前端已完成的 API 测试用例功能，设计并实现后端 API 接口，支持类似 Postman 的 API 测试功能，包括：

- API 请求集合管理
- 环境变量管理
- 请求历史记录
- 请求发送与响应处理
- 代码片段生成

### 1.2 技术栈

- **后端框架**: Django 4.1 + Django REST Framework 3.14.0
- **数据库**: MySQL (通过现有项目配置)
- **认证**: Session Authentication + Basic Authentication
- **API 文档**: drf-yasg (Swagger)

## 2. 数据模型设计

### 2.1 核心模型

#### 2.1.1 API 测试集合 (ApiTestCollection)

```python
class ApiTestCollection(ProjectModel):
    """API测试集合"""
    project_id = models.IntegerField(verbose_name='项目ID')
    name = models.CharField(max_length=200, verbose_name='集合名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')
    parent = models.IntegerField(default=0, verbose_name='父级集合ID')
    creator = models.IntegerField(verbose_name='创建人')
    is_folder = models.BooleanField(default=False, verbose_name='是否为文件夹')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_collection"
```

#### 2.1.2 API 测试用例 (ApiTestCase)

```python
class ApiTestCase(ProjectModel):
    """API测试用例"""
    collection = models.ForeignKey(ApiTestCollection, on_delete=models.CASCADE, verbose_name='所属集合')
    name = models.CharField(max_length=200, verbose_name='用例名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')

    # HTTP请求信息
    method = models.CharField(max_length=10, default='GET', verbose_name='HTTP方法')
    url = models.TextField(verbose_name='请求URL')

    # 请求配置
    headers = models.JSONField(default=dict, verbose_name='请求头')
    query_params = models.JSONField(default=dict, verbose_name='查询参数')
    path_variables = models.JSONField(default=dict, verbose_name='路径变量')

    # 请求体配置
    body_type = models.CharField(max_length=50, default='none', verbose_name='请求体类型')
    body_data = models.JSONField(default=dict, verbose_name='请求体数据')

    # 认证配置
    auth_type = models.CharField(max_length=50, default='none', verbose_name='认证类型')
    auth_config = models.JSONField(default=dict, verbose_name='认证配置')

    # 脚本配置
    pre_request_script = models.TextField(null=True, blank=True, verbose_name='前置脚本')
    post_request_script = models.TextField(null=True, blank=True, verbose_name='后置脚本')

    # 测试断言
    test_assertions = models.JSONField(default=list, verbose_name='测试断言')

    creator = models.IntegerField(verbose_name='创建人')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_case"
```

#### 2.1.3 环境变量 (ApiTestEnvironment)

```python
class ApiTestEnvironment(ProjectModel):
    """API测试环境变量"""
    project_id = models.IntegerField(verbose_name='项目ID')
    name = models.CharField(max_length=100, verbose_name='环境名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')
    variables = models.JSONField(default=dict, verbose_name='环境变量')
    secrets = models.JSONField(default=dict, verbose_name='敏感变量')
    is_global = models.BooleanField(default=False, verbose_name='是否为全局环境')
    creator = models.IntegerField(verbose_name='创建人')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_environment"
```

#### 2.1.4 执行历史 (ApiTestHistory)

```python
class ApiTestHistory(ProjectModel):
    """API测试执行历史"""
    project_id = models.IntegerField(verbose_name='项目ID')
    test_case = models.ForeignKey(ApiTestCase, null=True, on_delete=models.SET_NULL, verbose_name='关联测试用例')

    # 请求信息快照
    request_snapshot = models.JSONField(verbose_name='请求快照')

    # 响应信息
    response_status = models.IntegerField(null=True, verbose_name='响应状态码')
    response_headers = models.JSONField(default=dict, verbose_name='响应头')
    response_body = models.TextField(null=True, verbose_name='响应体')
    response_time = models.IntegerField(null=True, verbose_name='响应时间(ms)')
    response_size = models.IntegerField(null=True, verbose_name='响应大小(bytes)')

    # 测试结果
    test_results = models.JSONField(default=list, verbose_name='测试结果')
    is_success = models.BooleanField(default=True, verbose_name='是否成功')
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')

    executor = models.IntegerField(verbose_name='执行人')
    executed_at = models.DateTimeField(auto_now_add=True, verbose_name='执行时间')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_history"
```

### 2.2 模型管理器

#### 2.2.1 ApiTestCollectionManager

```python
class ApiTestCollectionManager(models.Manager):
    def get_project_collections(self, project_id, parent=0):
        """获取项目的集合树"""
        return self.filter(project_id=project_id, parent=parent, IsActive=True).order_by('sort_order', 'id')

    def get_collection_tree(self, project_id):
        """获取完整的集合树结构"""
        # 递归构建树结构的逻辑
        pass
```

#### 2.2.2 ApiTestCaseManager

```python
class ApiTestCaseManager(models.Manager):
    def get_collection_cases(self, collection_id):
        """获取集合下的测试用例"""
        return self.filter(collection_id=collection_id, IsActive=True).order_by('sort_order', 'id')
```

## 3. API 接口设计

### 3.1 URL 路由配置

#### 3.1.1 主路由 (api_test_case_urls.py)

```python
from django.urls import re_path
from teamvision.api.project.views import api_test_case_view

api_test_case_router = [
    # 集合管理
    re_path(r"(?P<project_id>\d+)/api-test/collections/$",
            api_test_case_view.ApiTestCollectionListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/(?P<collection_id>\d+)/$",
            api_test_case_view.ApiTestCollectionDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/tree/$",
            api_test_case_view.ApiTestCollectionTreeView.as_view()),

    # 测试用例管理
    re_path(r"(?P<project_id>\d+)/api-test/cases/$",
            api_test_case_view.ApiTestCaseListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/$",
            api_test_case_view.ApiTestCaseDetailView.as_view()),

    # 用例执行
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/execute/$",
            api_test_case_view.ApiTestCaseExecuteView.as_view()),

    # 环境管理
    re_path(r"(?P<project_id>\d+)/api-test/environments/$",
            api_test_case_view.ApiTestEnvironmentListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/environments/(?P<env_id>\d+)/$",
            api_test_case_view.ApiTestEnvironmentDetailView.as_view()),

    # 执行历史
    re_path(r"(?P<project_id>\d+)/api-test/history/$",
            api_test_case_view.ApiTestHistoryListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/history/(?P<history_id>\d+)/$",
            api_test_case_view.ApiTestHistoryDetailView.as_view()),

    # 代码生成
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/code-snippet/$",
            api_test_case_view.ApiTestCodeSnippetView.as_view()),
]
```

### 3.2 序列化器设计

#### 3.2.1 集合序列化器

```python
class ApiTestCollectionSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    case_count = serializers.SerializerMethodField()

    def get_children(self, obj):
        if obj.is_folder:
            children = ApiTestCollection.objects.filter(parent=obj.id, IsActive=True)
            return ApiTestCollectionTreeSerializer(children, many=True).data
        return []

    def get_case_count(self, obj):
        return ApiTestCase.objects.filter(collection=obj, IsActive=True).count()

    class Meta:
        model = ApiTestCollection
        fields = ['id', 'name', 'description', 'is_folder', 'children', 'case_count', 'CreationTime']
```

#### 3.2.2 测试用例序列化器

```python
class ApiTestCaseSerializer(serializers.ModelSerializer):
    collection_name = serializers.CharField(source='collection.name', read_only=True)
    creator_name = serializers.SerializerMethodField()

    def get_creator_name(self, obj):
        try:
            user = User.objects.get(id=obj.creator)
            return user.username
        except User.DoesNotExist:
            return "Unknown"

    class Meta:
        model = ApiTestCase
        fields = '__all__'
```

### 3.3 视图类设计

#### 3.3.1 集合管理视图

```python
class ApiTestCollectionListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/
    POST /api/project/{project_id}/api-test/collections/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        parent = self.request.GET.get('parent', 0)
        return ApiTestCollection.objects.get_project_collections(project_id, parent)

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)

class ApiTestCollectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/collections/{collection_id}/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        collection_id = self.kwargs['collection_id']
        return get_object_or_404(ApiTestCollection, id=collection_id, IsActive=True)
```

#### 3.3.2 测试用例执行视图

```python
class ApiTestCaseExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            environment_id = request.data.get('environment_id')

            # 执行API请求
            executor = ApiTestExecutor(test_case, environment_id)
            result = executor.execute()

            # 保存执行历史
            history = ApiTestHistory.objects.create(
                project_id=project_id,
                test_case=test_case,
                request_snapshot=executor.get_request_snapshot(),
                response_status=result.get('status_code'),
                response_headers=result.get('headers', {}),
                response_body=result.get('body', ''),
                response_time=result.get('response_time'),
                response_size=result.get('response_size'),
                test_results=result.get('test_results', []),
                is_success=result.get('is_success', True),
                error_message=result.get('error_message'),
                executor=request.user.id
            )

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'history_id': history.id,
                    'response': result
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)
```

## 4. 核心业务逻辑

### 4.1 API 请求执行器

#### 4.1.1 ApiTestExecutor 类

```python
import requests
import json
import time
from typing import Dict, Any, Optional

class ApiTestExecutor:
    """API测试执行器"""

    def __init__(self, test_case: ApiTestCase, environment_id: Optional[int] = None):
        self.test_case = test_case
        self.environment = self._load_environment(environment_id)
        self.session = requests.Session()

    def execute(self) -> Dict[str, Any]:
        """执行API测试"""
        try:
            # 1. 执行前置脚本
            self._execute_pre_script()

            # 2. 构建请求
            request_config = self._build_request()

            # 3. 发送请求
            start_time = time.time()
            response = self.session.request(**request_config)
            end_time = time.time()

            # 4. 处理响应
            result = self._process_response(response, end_time - start_time)

            # 5. 执行后置脚本和断言
            test_results = self._execute_post_script_and_assertions(response)
            result['test_results'] = test_results
            result['is_success'] = all(t.get('passed', False) for t in test_results)

            return result

        except Exception as e:
            return {
                'is_success': False,
                'error_message': str(e),
                'status_code': None,
                'headers': {},
                'body': '',
                'response_time': 0,
                'response_size': 0,
                'test_results': []
            }

    def _build_request(self) -> Dict[str, Any]:
        """构建请求配置"""
        # 处理URL和路径变量
        url = self._process_url()

        # 处理请求头
        headers = self._process_headers()

        # 处理查询参数
        params = self._process_query_params()

        # 处理请求体
        data, json_data, files = self._process_body()

        # 处理认证
        auth = self._process_auth()

        config = {
            'method': self.test_case.method,
            'url': url,
            'headers': headers,
            'params': params,
            'timeout': 30
        }

        if data:
            config['data'] = data
        if json_data:
            config['json'] = json_data
        if files:
            config['files'] = files
        if auth:
            config['auth'] = auth

        return config

    def _process_response(self, response: requests.Response, response_time: float) -> Dict[str, Any]:
        """处理响应数据"""
        try:
            response_body = response.text
            content_type = response.headers.get('content-type', '')

            # 尝试解析JSON
            if 'application/json' in content_type:
                try:
                    response_body = response.json()
                except:
                    pass

            return {
                'status_code': response.status_code,
                'status_text': response.reason,
                'headers': dict(response.headers),
                'body': response_body,
                'response_time': int(response_time * 1000),  # 转换为毫秒
                'response_size': len(response.content),
                'cookies': dict(response.cookies)
            }
        except Exception as e:
            raise Exception(f"处理响应失败: {str(e)}")
```

### 4.2 环境变量处理器

#### 4.2.1 EnvironmentProcessor 类

```python
class EnvironmentProcessor:
    """环境变量处理器"""

    def __init__(self, environment: Optional[ApiTestEnvironment] = None):
        self.variables = {}
        if environment:
            self.variables.update(environment.variables)
            self.variables.update(environment.secrets)

    def resolve_variables(self, text: str) -> str:
        """解析文本中的环境变量"""
        if not isinstance(text, str):
            return text

        import re
        pattern = r'\{\{(\w+)\}\}'

        def replace_var(match):
            var_name = match.group(1)
            return str(self.variables.get(var_name, match.group(0)))

        return re.sub(pattern, replace_var, text)

    def resolve_dict(self, data: Dict) -> Dict:
        """解析字典中的环境变量"""
        if not isinstance(data, dict):
            return data

        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = self.resolve_variables(value)
            elif isinstance(value, dict):
                result[key] = self.resolve_dict(value)
            else:
                result[key] = value
        return result
```

### 4.3 代码片段生成器

#### 4.3.1 CodeSnippetGenerator 类

```python
class CodeSnippetGenerator:
    """代码片段生成器"""

    def __init__(self, test_case: ApiTestCase, environment: Optional[ApiTestEnvironment] = None):
        self.test_case = test_case
        self.environment = environment
        self.processor = EnvironmentProcessor(environment)

    def generate_curl(self) -> str:
        """生成cURL命令"""
        url = self.processor.resolve_variables(self.test_case.url)
        method = self.test_case.method
        headers = self.processor.resolve_dict(self.test_case.headers)

        curl_parts = [f"curl -X {method}"]

        # 添加请求头
        for key, value in headers.items():
            curl_parts.append(f'-H "{key}: {value}"')

        # 添加请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body = json.dumps(self.test_case.body_data)
                curl_parts.append(f"-d '{body}'")

        curl_parts.append(f'"{url}"')
        return ' \\\n  '.join(curl_parts)

    def generate_python_requests(self) -> str:
        """生成Python requests代码"""
        template = '''import requests
import json

url = "{url}"
headers = {headers}
{body_section}
response = requests.{method}(url{params})

print(f"Status Code: {{response.status_code}}")
print(f"Response: {{response.text}}")'''

        url = self.processor.resolve_variables(self.test_case.url)
        headers = self.processor.resolve_dict(self.test_case.headers)
        method = self.test_case.method.lower()

        # 处理请求体
        body_section = ""
        params = ", headers=headers"

        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body_section = f"data = {json.dumps(self.test_case.body_data, indent=2)}"
                params += ", json=data"

        return template.format(
            url=url,
            headers=json.dumps(headers, indent=2),
            body_section=body_section,
            method=method,
            params=params
        )
```

## 5. 数据库迁移

### 5.1 迁移文件

```python
# migrations/0001_initial.py
from django.db import migrations, models
import django.db.models.deletion

class Migration(migrations.Migration):
    dependencies = [
        ('project', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApiTestCollection',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CreationTime', models.DateTimeField(auto_now_add=True)),
                ('IsActive', models.BooleanField(default=True)),
                ('project_id', models.IntegerField(verbose_name='项目ID')),
                ('name', models.CharField(max_length=200, verbose_name='集合名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('parent', models.IntegerField(default=0, verbose_name='父级集合ID')),
                ('creator', models.IntegerField(verbose_name='创建人')),
                ('is_folder', models.BooleanField(default=False, verbose_name='是否为文件夹')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
            ],
            options={
                'db_table': 'project_api_test_collection',
                'app_label': 'project',
            },
        ),
        # ... 其他模型的创建
    ]
```

## 6. 配置和部署

### 6.1 URL 配置更新

在 `teamvision/api/project/urlrouter/project_urls.py` 中添加：

```python
from teamvision.api.project.urlrouter.api_test_case_urls import api_test_case_router

urlpatterns = api_fortesting_router + api_version_router + api_member_router + api_issue_router
urlpatterns = urlpatterns + api_task_router + api_module_router + api_report_router + api_document_router
urlpatterns = urlpatterns + api_mindmap_router + requirement_router + api_product_space_router
urlpatterns = urlpatterns + api_test_application_router + testcase_router
urlpatterns = urlpatterns + testplan_router + testreport_router + autocase_router + case_review_router
urlpatterns = urlpatterns + api_test_case_router  # 新增
```

### 6.2 权限配置

```python
# 在相关视图中配置权限
class ApiTestCaseListView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]  # 需要登录

    def get_queryset(self):
        # 确保用户只能访问有权限的项目
        project_id = self.kwargs['project_id']
        # 添加项目权限检查逻辑
        return super().get_queryset()
```

## 7. 测试策略

### 7.1 单元测试

```python
# tests/test_api_test_case.py
from django.test import TestCase
from teamvision.project.models import ApiTestCase, ApiTestCollection

class ApiTestCaseTestCase(TestCase):
    def setUp(self):
        self.collection = ApiTestCollection.objects.create(
            project_id=1,
            name="Test Collection",
            creator=1
        )

    def test_create_api_test_case(self):
        case = ApiTestCase.objects.create(
            collection=self.collection,
            name="Test API",
            method="GET",
            url="https://api.example.com/test",
            creator=1
        )
        self.assertEqual(case.name, "Test API")
        self.assertEqual(case.method, "GET")
```

### 7.2 API 测试

```python
# tests/test_api_views.py
from rest_framework.test import APITestCase
from django.contrib.auth.models import User

class ApiTestCaseViewTestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user('testuser', '<EMAIL>', 'testpass')
        self.client.force_authenticate(user=self.user)

    def test_create_collection(self):
        data = {
            'name': 'New Collection',
            'description': 'Test collection',
            'is_folder': True
        }
        response = self.client.post('/api/project/1/api-test/collections/', data)
        self.assertEqual(response.status_code, 201)
```

## 8. 性能优化

### 8.1 数据库优化

- 为常用查询字段添加索引
- 使用 select_related 和 prefetch_related 优化查询
- 实现分页功能

### 8.2 缓存策略

```python
from django.core.cache import cache

class ApiTestCollectionListView(generics.ListCreateAPIView):
    def get_queryset(self):
        project_id = self.kwargs['project_id']
        cache_key = f"api_collections_{project_id}"

        queryset = cache.get(cache_key)
        if queryset is None:
            queryset = super().get_queryset()
            cache.set(cache_key, queryset, 300)  # 缓存5分钟

        return queryset
```

## 9. 安全考虑

### 9.1 输入验证

- 对所有用户输入进行严格验证
- 防止 SQL 注入和 XSS 攻击
- 限制请求体大小

### 9.2 权限控制

- 确保用户只能访问有权限的项目数据
- 实现细粒度的权限控制
- 记录敏感操作日志

## 10. 监控和日志

### 10.1 日志记录

```python
import logging

logger = logging.getLogger(__name__)

class ApiTestCaseExecuteView(APIView):
    def post(self, request, project_id, case_id):
        logger.info(f"User {request.user.id} executing API test case {case_id}")
        try:
            # 执行逻辑
            pass
        except Exception as e:
            logger.error(f"API test execution failed: {str(e)}")
            raise
```

### 10.2 性能监控

- 监控 API 响应时间
- 记录执行成功率
- 监控数据库查询性能

## 11. 实施计划

### 11.1 开发阶段

1. **第一阶段**: 数据模型设计和创建
2. **第二阶段**: 基础 CRUD 接口实现
3. **第三阶段**: API 执行器和环境变量处理
4. **第四阶段**: 代码生成和高级功能
5. **第五阶段**: 测试和优化

### 11.2 部署步骤

1. 创建数据库迁移文件
2. 执行数据库迁移
3. 更新 URL 配置
4. 部署后端代码
5. 前后端联调测试

这个设计文档提供了完整的后端开发方案，涵盖了数据模型、API 接口、核心业务逻辑、测试策略等各个方面，可以支撑前端已完成的 API 测试用例功能。
