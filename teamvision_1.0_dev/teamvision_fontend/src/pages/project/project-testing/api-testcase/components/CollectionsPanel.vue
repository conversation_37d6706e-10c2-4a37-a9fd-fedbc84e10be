<template>
  <div class="collections-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>API 用例集</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <el-input v-model="searchQuery" placeholder="Search" prefix-icon="el-icon-search" size="mini"
        class="search-input" />

      <div class="action-buttons">
        <el-button type="primary" size="mini">
          <i class="el-icon-plus"></i> New
        </el-button>
        <div class="header-icons">
          <el-tooltip content="Add" placement="bottom">
            <el-button icon="el-icon-plus" size="mini" circle @click="showNewCollectionDialog"></el-button>
          </el-tooltip>
          <el-tooltip content="Download" placement="bottom">
            <el-button icon="el-icon-download" size="mini" circle></el-button>
          </el-tooltip>
          <el-tooltip content="More" placement="bottom">
            <el-button icon="el-icon-more" size="mini" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="collection-tree">
      <el-tree :data="treeData" :props="treeProps" :filter-node-method="filterNode" node-key="id"
        :expand-on-click-node="false" :highlight-current="true" @node-click="handleNodeClick">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <i :class="getNodeIcon(data)" class="tree-icon"></i>
          <el-tag v-if="data.method" :type="getMethodTagType(data.method)" size="mini" class="method-tag">
            {{ data.method }}
          </el-tag>
          <span class="tree-label">{{ node.label }}</span>
          <i v-if="data.status === 'success'" class="el-icon-success status-icon success"></i>
          <i v-else-if="data.status === 'error'" class="el-icon-error status-icon error"></i>
          <el-dropdown v-if="data.type === 'request'" trigger="click" @command="handleCommand" class="tree-actions">
            <span class="el-dropdown-link">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="{ action: 'edit', data }">Edit</el-dropdown-item>
              <el-dropdown-item :command="{ action: 'duplicate', data }">Duplicate</el-dropdown-item>
              <el-dropdown-item :command="{ action: 'delete', data }" divided>Delete</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollectionsPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      treeProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [
        {
          id: 'login',
          type: 'folder',
          name: 'login',
          children: [
            {
              id: 'login-request',
              type: 'request',
              name: 'login',
              method: 'GET',
              status: 'success'
            }
          ]
        }
      ]
    }
  },
  watch: {
    searchQuery(val) {
      this.$refs.tree && this.$refs.tree.filter(val);
    }
  },
  methods: {
    handleNodeClick(data) {
      console.log('选择项目:', data);
      // 这里应该触发选择事件
    },
    showNewCollectionDialog() {
      this.$prompt('请输入集合名称', '新建集合', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '集合名称不能为空'
      }).then(({ value }) => {
        const newCollection = {
          id: 'collection_' + Date.now(),
          type: 'folder',
          name: value.trim(),
          children: []
        };
        this.treeData.push(newCollection);
        this.$message.success('集合创建成功');
      }).catch(() => {
        this.$message.info('已取消创建');
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getNodeIcon(data) {
      if (data.type === 'folder') {
        return 'el-icon-folder';
      } else if (data.type === 'request') {
        return 'el-icon-document';
      }
      return 'el-icon-document';
    },
    getMethodTagType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      };
      return types[method] || 'success';
    },
    handleCommand(command) {
      const { action, data } = command;
      switch (action) {
        case 'edit':
          console.log('编辑请求:', data);
          break;
        case 'duplicate':
          console.log('复制请求:', data);
          break;
        case 'delete':
          this.$confirm('确定要删除这个请求吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 这里应该实现删除逻辑
            this.$message.success('删除成功');
          }).catch(() => {
            this.$message.info('已取消删除');
          });
          break;
      }
    }
  }
}
</script>

<style scoped>
.collections-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.collection-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.tree-icon {
  margin-right: 8px;
  font-size: 14px;
  color: #666;
}

.method-tag {
  margin-right: 6px;
}

.tree-label {
  flex: 1;
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.status-icon {
  margin-right: 8px;
  font-size: 12px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.error {
  color: #f56c6c;
}

.tree-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .tree-actions {
  opacity: 1;
}

.el-dropdown-link {
  cursor: pointer;
  color: #666;
  font-size: 12px;
}

.el-dropdown-link:hover {
  color: #409eff;
}
</style>
