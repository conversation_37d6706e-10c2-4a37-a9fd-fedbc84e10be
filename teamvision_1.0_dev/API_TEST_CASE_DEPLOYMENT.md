# API测试用例功能部署说明

## 概述

本文档说明如何部署和配置API测试用例功能的后端代码。该功能基于Django REST Framework实现，提供类似Postman的API测试能力。

## 已完成的开发内容

### 1. 数据模型 (Models)
- `ApiTestCollection`: API测试集合模型
- `ApiTestCase`: API测试用例模型  
- `ApiTestEnvironment`: API测试环境变量模型
- `ApiTestHistory`: API测试执行历史模型

### 2. 序列化器 (Serializers)
- `ApiTestCollectionSerializer`: 集合序列化器
- `ApiTestCaseSerializer`: 测试用例序列化器
- `ApiTestEnvironmentSerializer`: 环境变量序列化器
- `ApiTestHistorySerializer`: 执行历史序列化器
- `ApiTestCaseExecuteSerializer`: 执行请求序列化器
- `ApiTestCodeSnippetSerializer`: 代码片段生成序列化器

### 3. API视图 (Views)
- `ApiTestCollectionListView`: 集合列表和创建
- `ApiTestCollectionDetailView`: 集合详情、更新、删除
- `ApiTestCollectionTreeView`: 集合树形结构
- `ApiTestCaseListView`: 测试用例列表和创建
- `ApiTestCaseDetailView`: 测试用例详情、更新、删除
- `ApiTestCaseExecuteView`: 测试用例执行
- `ApiTestEnvironmentListView`: 环境变量列表和创建
- `ApiTestEnvironmentDetailView`: 环境变量详情、更新、删除
- `ApiTestHistoryListView`: 执行历史列表
- `ApiTestHistoryDetailView`: 执行历史详情
- `ApiTestCodeSnippetView`: 代码片段生成

### 4. 核心业务逻辑
- `ApiTestExecutor`: API测试执行器
- `EnvironmentProcessor`: 环境变量处理器
- `CodeSnippetGenerator`: 代码片段生成器

### 5. URL路由配置
- 已配置完整的API路由
- 已集成到主路由系统中

### 6. 数据库迁移
- 创建了迁移文件 `0003_api_test_case_models.py`

## 部署步骤

### 1. 环境准备

确保已安装以下依赖：
```bash
pip install Django==4.1
pip install djangorestframework==3.14.0
pip install requests
pip install mysqlclient  # 或 pymysql
```

### 2. 数据库迁移

执行以下命令创建数据库表：

```bash
cd teamvision
python manage.py makemigrations project
python manage.py migrate project
```

### 3. 重启服务

重启Django应用服务器：
```bash
# 如果使用uwsgi
touch restart.txt

# 或者重启uwsgi进程
sudo systemctl restart teamvision-web
```

### 4. 验证部署

访问以下API端点验证部署是否成功：

```bash
# 获取项目的API测试集合
GET /api/project/{project_id}/api-test/collections/

# 获取集合树形结构
GET /api/project/{project_id}/api-test/collections/tree/

# 创建测试用例
POST /api/project/{project_id}/api-test/cases/

# 执行测试用例
POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
```

## API接口文档

### 集合管理

#### 获取集合列表
```
GET /api/project/{project_id}/api-test/collections/
```

#### 创建集合
```
POST /api/project/{project_id}/api-test/collections/
Content-Type: application/json

{
    "name": "集合名称",
    "description": "集合描述",
    "is_folder": true,
    "parent": 0
}
```

#### 获取集合树
```
GET /api/project/{project_id}/api-test/collections/tree/
```

### 测试用例管理

#### 获取测试用例列表
```
GET /api/project/{project_id}/api-test/cases/
GET /api/project/{project_id}/api-test/cases/?collection_id=123
```

#### 创建测试用例
```
POST /api/project/{project_id}/api-test/cases/
Content-Type: application/json

{
    "collection": 1,
    "name": "测试用例名称",
    "method": "POST",
    "url": "https://api.example.com/users",
    "headers": {
        "Content-Type": "application/json"
    },
    "body_type": "raw",
    "body_data": {
        "name": "John Doe",
        "email": "<EMAIL>"
    }
}
```

#### 执行测试用例
```
POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
Content-Type: application/json

{
    "environment_id": 1,
    "override_config": {
        "headers": {
            "Authorization": "Bearer token"
        }
    }
}
```

### 环境管理

#### 获取环境列表
```
GET /api/project/{project_id}/api-test/environments/
```

#### 创建环境
```
POST /api/project/{project_id}/api-test/environments/
Content-Type: application/json

{
    "name": "开发环境",
    "variables": {
        "base_url": "https://dev-api.example.com",
        "api_key": "dev-key-123"
    },
    "secrets": {
        "password": "secret-password"
    }
}
```

### 执行历史

#### 获取执行历史
```
GET /api/project/{project_id}/api-test/history/
GET /api/project/{project_id}/api-test/history/?case_id=123&limit=20
```

### 代码生成

#### 生成代码片段
```
POST /api/project/{project_id}/api-test/cases/{case_id}/code-snippet/
Content-Type: application/json

{
    "language": "curl",
    "environment_id": 1
}
```

支持的语言类型：
- `curl`: cURL命令
- `python`: Python Requests
- `javascript`: JavaScript Fetch
- `java`: Java OkHttp
- `go`: Go HTTP

## 注意事项

### 1. 权限控制
当前使用 `AllowAny` 权限，生产环境中应该根据需要调整权限设置。

### 2. 缓存配置
部分接口使用了缓存来提高性能，确保Redis或其他缓存服务正常运行。

### 3. 请求限制
API执行功能可能会发起外部HTTP请求，建议配置适当的超时和重试机制。

### 4. 日志监控
建议监控API执行的成功率和响应时间，及时发现问题。

### 5. 安全考虑
- 环境变量中的敏感信息已做脱敏处理
- 建议对外部请求进行白名单限制
- 考虑添加请求频率限制

## 故障排除

### 1. 迁移失败
如果数据库迁移失败，检查：
- 数据库连接配置
- 表名冲突
- 字段类型兼容性

### 2. API请求失败
如果API请求失败，检查：
- URL路由配置
- 序列化器验证
- 权限设置

### 3. 执行器错误
如果测试执行失败，检查：
- 网络连接
- 请求超时设置
- 环境变量解析

## 后续扩展

### 1. 脚本执行
可以集成JavaScript引擎来支持前置和后置脚本执行。

### 2. 断言增强
可以添加更多类型的断言，如JSON路径断言、正则表达式断言等。

### 3. 批量执行
可以添加集合批量执行功能。

### 4. 性能测试
可以扩展支持并发测试和性能测试。

### 5. 导入导出
可以添加Postman格式的导入导出功能。
