# coding=utf-8
'''
Created on 2015-10-22

@author: zhangtian<PERSON>
'''
import time

from django.db import connection

from gatesidelib.common.simplelogger import SimpleLogger
from model_managers.model_manager import ModelManager


class TaskManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TaskManager, self).get_queryset().filter(IsActive=1)

    def get(self, taskid):
        result = None
        try:
            result = super(TaskManager, self).get_queryset().get(id=taskid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_tasks(self, project_id):
        result = list()
        try:
            result = self.all().filter(ProjectID=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_child_tasks(self, task_id, status=-1):
        result = list()
        try:
            if status < 0:
                result = self.all().filter(Parent=int(task_id))
            else:
                result = self.all().filter(Parent=int(task_id)).filter(Status=int(status))
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class RequirementManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(RequirementManager, self).get_queryset().filter(IsActive=1)

    def get(self, req_id):
        result = None
        try:
            result = super(RequirementManager, self).get_queryset().get(id=req_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_requirements(self, project_id):
        result = list()
        try:
            result = self.all().filter(ProjectID=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class RequirementTaskManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(RequirementTaskManager, self).get_queryset().filter(IsActive=1)

    def get(self, req_id):
        result = None
        try:
            result = super(RequirementTaskManager, self).get_queryset().get(id=req_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_tasks(self, req_id):
        result = list()
        try:
            result = self.all().filter(RequirementID=req_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class FortestingTaskManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(FortestingTaskManager, self).get_queryset().filter(IsActive=1)

    def get(self, map_id):
        result = None
        try:
            result = super(FortestingTaskManager, self).get_queryset().get(id=map_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_tasks(self, fortesting_id):
        result = list()
        try:
            result = self.all().filter(FortestingID=fortesting_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TaskOwnerManager(ModelManager):
    '''
    classdocs
    '''

    def all(self, is_active=True):
        if is_active == True:
            return super(TaskOwnerManager, self).get_queryset().filter(IsActive=1)
        else:
            return super(TaskOwnerManager, self).get_queryset()

    def get(self, id):
        result = None
        try:
            result = super(TaskOwnerManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_owners(self, task_id):
        result = list()
        try:
            result = self.all().filter(Task=task_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_task_owner(self, task_id, owner_id):
        result = None
        owner = self.all().filter(Task=task_id).filter(Owner=owner_id)
        if len(owner) > 0:
            result = owner[0]
        return result

    def get_owners_byversion(self, version):
        result = None
        try:
            result = self.all().filter(Version=version)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TaskStatusManager(ModelManager):
    """
    class docs
    """

    def all(self, is_active=True):
        if is_active == True:
            return super(TaskStatusManager, self).get_queryset().filter(IsActive=1)
        else:
            return super(TaskStatusManager, self).get_queryset()

    def get(self, id):
        result = None
        try:
            result = super(TaskStatusManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_by_value(self, type, value):
        result = None
        try:
            result = self.all().filter(Type=type).filter(Status=value)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TaskTypeManager(ModelManager):
    '''
    classdocs
    '''

    def all(self, is_active=True):
        if is_active == True:
            return super(TaskTypeManager, self).get_queryset().filter(IsActive=1)
        else:
            return super(TaskStatusManager, self).get_queryset()

    def get(self, id):
        result = None
        try:
            result = super(TaskTypeManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TagOwnerManager(ModelManager):
    '''
    classdocs
    '''

    def all(self, is_active=True):
        if is_active == True:
            return super(TagOwnerManager, self).get_queryset().filter(IsActive=1)
        else:
            return super(TagOwnerManager, self).get_queryset()

    def get(self, id):
        result = None
        try:
            result = super(TagOwnerManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_tags(self, owner, owner_type):
        result = list()
        try:
            result = self.all().filter(Owner=owner).filter(OwnerType=owner_type)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_tag_byid(self, owner, tag_id):
        result = list()
        try:
            result = self.all().filter(Owner=owner).filter(TagID=tag_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TaskDependencyManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TaskDependencyManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TaskDependencyManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_dependencies(self, version):
        result = None
        try:
            result = self.all().filter(Version=version)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_task_dependency(self, task_id):
        result = None
        try:
            result = self.all().filter(Task=task_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class VersionManager(ModelManager):
    '''
    classdocs
    '''

    def all(self, version_filed=False):
        if version_filed is None:
            result = super(VersionManager, self).get_queryset().filter(IsActive=1)
        else:
            result = super(VersionManager, self).get_queryset().filter(IsActive=1).filter(VersionFiled=version_filed)

        return result

    def get(self, version_id):
        result = None
        try:
            result = super(VersionManager, self).get_queryset().get(id=version_id)
        except Exception as ex:
            SimpleLogger.warning("version_id=" + str(version_id) + ":" + str(ex))
        return result

    def get_versions(self, project_id, version_filed=False):
        result = list()
        try:
            result = self.all(version_filed).filter(VProjectID=project_id).order_by('-id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class FortestingManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(FortestingManager, self).get_queryset().filter(IsActive=1)

    def get(self, tp_id):
        result = None
        try:
            result = super(FortestingManager, self).get_queryset().get(id=tp_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def project_fortestings(self, project_id):
        result = list()
        try:
            result = self.all().filter(ProjectID=int(project_id))
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class WebHookManager(ModelManager):
    '''
    classdocs
    '''

    def all(self, project_id):
        return super(WebHookManager, self).get_queryset().filter(WHProjectID=project_id).filter(IsActive=1)

    def get(self, webhook_id):
        result = None
        try:
            result = super(WebHookManager, self).get_queryset().get(id=webhook_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_build_webhook(self, project_id):
        result = None
        try:
            result = \
                super(WebHookManager, self).get_queryset().all().filter(WHProjectID=project_id).filter(
                    WHCatagory=1).filter(
                    WHIsDefault=1)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class BuildHistoryManager(ModelManager):
    '''
    classdocs
    '''


class CodeUrlManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(CodeUrlManager, self).get_queryset().filter(IsActive=1)

    def get(self, code_url_id):
        result = None
        try:
            result = super(CodeUrlManager, self).get_queryset().get(id=code_url_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TopicTagMapManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TopicTagMapManager, self).get_queryset().filter(IsActive=1)

    def get(self, tag_mapid):
        result = None
        try:
            result = super(TopicTagMapManager, self).get_queryset().get(id=tag_mapid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_topic_tags(self, topic_id):
        result = None
        try:
            result = super(TopicTagMapManager, self).get_queryset().filter(TopicID=topic_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestCaseAttachmentManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestCaseAttachmentManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestCaseAttachmentManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_attachments(self, case_id, case_type):
        result = None
        try:
            result = super(TestCaseAttachmentManager, self).get_queryset().filter(CaseID=case_id).filter(
                CaseType=case_type)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestCaseIssueManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestCaseIssueManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestCaseIssueManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_issues(self, case_id, case_type):
        result = None
        try:
            result = super(TestCaseIssueManager, self).get_queryset().filter(CaseID=case_id).filter(CaseType=case_type)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestCaseTagManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestCaseTagManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestCaseTagManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_tags(self, case_id):
        result = None
        try:
            result = super(TestCaseTagManager, self).get_queryset().filter(CaseID=case_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestPlanManager(ModelManager):
    """
        TestPlanManager
    """

    def all(self):
        return super(TestPlanManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestPlanManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.warning("plan_id=" + str(id) + ":" + str(ex))
        return result

    def get_project_plans(self, project_id):
        result = None
        try:
            result = self.all().filter(Project=project_id).order_by('-id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_plans(self, version_id):
        result = None
        try:
            result = self.all().filter(Version=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_creator_plans(self, user_id):
        result = None
        try:
            result = self.all().filter(Creator=user_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_owner_plans(self, owner_id):
        result = None
        try:
            result = self.all().filter(Owner=owner_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestPlanCaseManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestPlanCaseManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestPlanCaseManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_plan_cases(self, plan_id):
        result = list()
        try:
            result = self.all().filter(TestPlan=plan_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_my_plan_cases(self, plan_id):
        result = list()
        try:
            result = self.all().filter(TestPlan__in=plan_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_descendant_case_list(self, plan_id, case_id):
        sql = "select * from (select t1.id, t1.TestPlan, t1.TestCase, t1.IsGroup, t1.TestResult, t1.Parent, if(find_in_set(Parent, @pids) > 0, @pids := concat(@pids, ',', TestCase), 0) as ischild from(" + \
              "select * from project_test_plan_case t WHERE TestPlan={P_ID}) t1,(select @pids := {Case_ID}) t2) t3 where ischild != 0 and IsGroup=0"
        sql = sql.replace("{P_ID}", str(plan_id)).replace("{Case_ID}", str(case_id))
        queryset = list()
        try:
            queryset = super(TestPlanCaseManager, self).get_queryset().raw(sql)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return queryset

    def get_exec_count_query(self, plan_id, case_id):
        # 查询一次0.04秒
        # sql = "SELECT COUNT(*) AS TotalCount, SUM(CASE WHEN TestResult != 0 THEN 1 ELSE 0 END) AS ExecCount FROM (SELECT * FROM (" + \
        #       "SELECT t1.id, t1.TestPlan,  t1.TestCase, t1.IsGroup, t1.TestResult, t1.Parent, IF(FIND_IN_SET(Parent, @pids) > 0, @pids := CONCAT(@pids, ',', TestCase), 0) AS ischild FROM (" + \
        #       "SELECT * FROM project_test_plan_case t  WHERE TestPlan = {P_ID}) t1, (SELECT @pids := {Case_ID}) t2) t3 WHERE ischild != 0 AND IsGroup = 0) t4;"

        sql = "select t1.* from (select id,TestCase,Parent,IsGroup,TestPlan from  (select * from project_test_plan_case order by Parent, id) case_sorted, (select @pv := {Case_ID}) initialisation where find_in_set(Parent, @pv) and length(@pv := concat(@pv, ',', TestCase)) order by testplan) t1 where t1.TestPlan={P_ID}"

        sql = sql.replace("{P_ID}", str(plan_id)).replace("{Case_ID}", str(case_id))
        print(sql)
        result = [0, 0]
        with connection.cursor() as cursor:
            cursor.execute(sql)
            rows = cursor.fetchall()
            result = [rows[0][0], rows[0][1]]
        return result


class TestCaseExecResultManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestCaseExecResultManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestCaseExecResultManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_exec_result(self, case_map_id):
        result = list()
        try:
            result = self.all().filter(case_id=case_map_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestPlanFortestingManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TestPlanFortestingManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestPlanFortestingManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_plan_fortesting(self, plan_id):
        result = list()
        try:
            result = self.all().filter(TestPlan=plan_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class MindFileManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(MindFileManager, self).get_queryset().filter(IsActive=1)

    def get(self, file_id):
        result = None
        try:
            result = super(MindFileManager, self).get_queryset().get(id=file_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_by_project(self, project_id):
        result = list()
        try:
            result = self.all().filter(ProjectID=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_by_version(self, version_id):
        result = list()
        try:
            result = self.all().filter(VersionID=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class MindTopicManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(MindTopicManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(MindTopicManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_by_originalid(self, originalid, file_id):
        result = None
        try:
            result = self.all().filter(MindFileID=file_id).filter(OriginalID=originalid)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_children(self, id, file_id):
        result = list()
        try:
            result = super(MindTopicManager, self).get_queryset().filter(MindFileID=file_id).filter(Parent=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class TestCaseManager(ModelManager):
    """
        TestCase Model Manager
    """

    def all(self):
        return super(TestCaseManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(TestCaseManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.warning("case_id=" + str(id) + ":" + str(ex))
        return result

    def get_children(self, group_id):
        result = list()
        try:
            result = super(TestCaseManager, self).get_queryset().filter(Parent=group_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_descendant(self, group_id):
        '''
        :param group_id:
        :return: type: RawQuerySet
        '''
        sql = "select id,Title,'Desc',ExpectResult,Precondition,Priority,Parent,Project,Module,IsGroup from" + \
              " (select * from project_test_case order by Parent, id) case_sorted," + \
              " (select @pv := '{ParentID}') initialisation" + \
              " where find_in_set(Parent, @pv) and length(@pv := concat(@pv, ',', id))"
        sql = sql.replace("{ParentID}", str(group_id))
        result = list()
        try:
            case_set = super(TestCaseManager, self).get_queryset().raw(sql)
            result = case_set
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_descendant_case_count(self, project_id, case_id):
        """
        :param project_id:
        :param case_id:
        :return:
        """
        sql = "select id, Title, IsGroup from (select t1.id, t1.Title, t1.IsGroup," + \
              " if(find_in_set(Parent, @pids) > 0, @pids := concat(@pids, ',', id), 0) as ischild from" + \
              " (select id,Title,Parent,IsGroup from project_test_case t WHERE Project={P_ID}) t1," + \
              " (select @pids := {Case_ID}) t2) t3 where ischild != 0 and IsGroup=0"

        sql1 = "select id,Title,Parent,Project,IsGroup from (select * from project_test_case order by Parent, id) case_sorted," + \
               " (select @pv := {Case_ID}) initialisation where find_in_set(Parent, @pv) and length(@pv := concat(@pv, ',', id))"

        sql11 = "SELECT COUNT(*) AS count_isgroup_zero FROM (SELECT id, Parent, IsGroup FROM (SELECT * FROM project_test_case WHERE Project={P_ID} ORDER BY Parent, id) case_sorted,(SELECT @pv := {Case_ID}) initialisation WHERE FIND_IN_SET(Parent, @pv) AND LENGTH(@pv := CONCAT(@pv, ',', id))) AS subquery WHERE IsGroup = 0"

        sql = sql.replace("{P_ID}", str(project_id)).replace("{Case_ID}", str(case_id))
        # print(sql)
        try:
            case_set = super(TestCaseManager, self).get_queryset().raw(sql)
            result = len(case_set)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class MemberManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(MemberManager, self).get_queryset().filter(IsActive=1)

    def get(self, memberid):
        result = None
        try:
            result = super(MemberManager, self).get_queryset().get(id=memberid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_members(self, projectid):
        return self.all().filter(PMProjectID=projectid)

    def get_member(self, projectid, userid):
        result = None
        try:
            result = self.get_members(projectid).filter(PMMember=userid)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProductSpaceUserManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProductSpaceUserManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(ProductSpaceUserManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_space_users(self, space):
        return self.all().filter(ProductSpace=space)


class TagManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(TagManager, self).get_queryset().filter(IsActive=1)

    def get(self, tag_id):
        result = None
        try:
            result = super(TagManager, self).get_queryset().get(id=tag_id)
        except Exception as ex:
            SimpleLogger.warning(str(tag_id) + str(ex))
        return result

    def has_tag(self, tag_name):
        result = super(TagManager, self).get_queryset().filter(TagName=tag_name)
        if len(result) > 0:
            return True
        else:
            return False


class ProjectManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProjectManager, self).get_queryset().filter(IsActive=1)

    def get(self, projectid):
        result = None
        try:
            result = super(ProjectManager, self).get_queryset().get(id=projectid)
        except Exception as ex:
            SimpleLogger.exception("project_id=" + str(projectid) + ":" + str(ex))
        return result

    def check_value_exits(self, filed_name, filed_value):
        result = None
        try:
            if filed_name == "PBTitle":
                result = self.all().filter(PBTitle=filed_value)
            if filed_name == "PBKey":
                result = self.all().filter(PBKey=filed_value)

        except Exception as ex:
            SimpleLogger.exception(ex)
        if len(result) == 0:
            return False
        else:
            return True


class RoleManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(RoleManager, self).get_queryset().filter(IsActive=1)

    def get(self, role_id):
        result = None
        try:
            result = super(RoleManager, self).get_queryset().get(id=role_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ArchiveManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ArchiveManager, self).get_queryset().filter(IsActive=1)

    def get_project_archives(self, projectid):
        result = None
        try:
            result = super(ArchiveManager, self).get_queryset().filter(ProjectID=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_archives(self, version_id):
        result = None
        try:
            result = super(ArchiveManager, self).get_queryset().filter(VersionID=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, archive_id):
        result = None
        try:
            result = super(ArchiveManager, self).get_queryset().filter(id=archive_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectDocumentManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProjectDocumentManager, self).get_queryset().filter(IsActive=1)

    def get_project_documents(self, projectid):
        result = None
        try:
            result = super(ProjectDocumentManager, self).get_queryset().filter(ProjectID=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_child_documents(self, document_id):
        result = list()
        try:
            result = super(ProjectDocumentManager, self).get_queryset().filter(Parent=document_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, document_id):
        result = None
        try:
            result = super(ProjectDocumentManager, self).get_queryset().get(id=document_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(IssueManager, self).get_queryset().filter(IsActive=1)

    def get_project_issue(self, projectid):
        result = None
        try:
            result = self.all().filter(Project=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_issue(self, version_id):
        result = None
        try:
            result = self.all().filter(Version=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_processor_issue(self, processor_id):
        result = None
        try:
            result = self.all().filter(Processor=processor_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_reporter_issue(self, reporter_id):
        result = None
        try:
            result = self.all().filter(Creator=reporter_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, issue_id):
        result = None
        try:
            result = super(IssueManager, self).get_queryset().get(id=issue_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueDailyStatisticsManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(IssueDailyStatisticsManager, self).get_queryset().filter(IsActive=1)

    def get_project_issue_statistics(self, projectid):
        result = None
        try:
            result = self.all().filter(ProjectID=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_issue_statistics(self, version_id):
        result = None
        try:
            result = self.all().filter(VersionID=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, issue_id):
        result = None
        try:
            result = super(IssueDailyStatisticsManager, self).get_queryset().get(id=issue_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueVersionStatisticsManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(IssueVersionStatisticsManager, self).get_queryset().filter(IsActive=1)

    def get_project_issue_statistics(self, projectid):
        result = None
        try:
            result = self.all().filter(ProjectID=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_issue_statistics(self, version_id):
        result = None
        try:
            result = self.all().filter(VersionID=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, issue_id):
        result = None
        try:
            result = super(IssueVersionStatisticsManager, self).get_queryset().get(id=issue_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueConfigFieldManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        all_objects = super(IssueConfigFieldManager, self).get_queryset().filter(IsActive=1)
        return all_objects

    def get_byvalue(self, config_value):
        result = None
        try:
            result = super(IssueConfigFieldManager, self).get_queryset().filter(Value=config_value)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, id):
        result = None
        try:
            result = super(IssueConfigFieldManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectIssueResolvedResultManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        all_objects = super(ProjectIssueResolvedResultManager, self).get_queryset().filter(IsActive=1)
        return all_objects

    def get_byvalue(self, config_value):
        result = None
        try:
            result = super(ProjectIssueResolvedResultManager, self).get_queryset().filter(Value=config_value)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get(self, id):
        result = None
        try:
            result = super(ProjectIssueResolvedResultManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result
    

class ProjectOSVersionManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProjectOSVersionManager, self).get_queryset().filter(IsActive=1)

    def get_byvalue(self, os, config_value):
        result = None
        try:
            result = super(ProjectOSVersionManager, self).get_queryset().filter(Value=config_value).filter(OS=os)[0]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_by_os(self, os_value):
        result = None
        try:
            result = super(ProjectOSVersionManager, self).get_queryset().filter(OS=os_value)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProductSpaceManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProductSpaceManager, self).get_queryset().filter(IsActive=1)

    def get(self, space_id):
        result = None
        try:
            result = super(ProductSpaceManager, self).get_queryset().get(id=space_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ModuleManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ModuleManager, self).get_queryset().filter(IsActive=1)

    def get(self, moduleid):
        result = None
        try:
            result = super(ModuleManager, self).get_queryset().get(id=moduleid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def project_modules(self, project_id):
        result = None
        try:
            result = self.all().filter(ProjectID=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueActivityManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(IssueActivityManager, self).get_queryset().filter(IsActive=1)

    def get(self, activity_id):
        result = None
        try:
            result = super(IssueActivityManager, self).get_queryset().get(id=activity_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def issue_activity(self, issue_id):
        result = None
        try:
            result = self.all().filter(Issue=issue_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class IssueFilterManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(IssueFilterManager, self).get_queryset().filter(IsActive=1)

    def get(self, filter_id):
        result = None
        try:
            result = super(IssueFilterManager, self).get_queryset().get(id=filter_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def project_issue_filter(self, project_id):
        result = None
        try:
            result = self.all().filter(Project=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def user_issue_filter(self, user_id):
        result = None
        try:
            result = self.all().filter(Creator=user_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectTestApplicationTimeStatisticsManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestApplicationTimeStatisticsManager, self).get_queryset()

    def get(self, stat_id):
        result = None
        try:
            result = self.all().get(id=stat_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectTestApplicationNumberStatisticsManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestApplicationNumberStatisticsManager, self).get_queryset()

    def get(self, stat_id):
        result = None
        try:
            result = self.all().get(id=stat_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectTestReportManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestReportManager, self).get_queryset()

    def get(self, report_id):
        result = None
        try:
            result = self.all().get(id=report_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_reports(self, project_id):
        return self.all().filter(Project=project_id)


class ProjectTestReportTestPlanManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestReportTestPlanManager, self).get_queryset()

    def get(self, report_id):
        result = None
        try:
            result = self.all().get(id=report_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_report_plans(self, reportID):
        return self.all().filter(Report=reportID)


class ProjectTestReportRequirementManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestReportRequirementManager, self).get_queryset()

    def get(self, report_id):
        result = None
        try:
            result = self.all().get(id=report_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_report_reqs(self, reportID):
        return self.all().filter(Report=reportID)


class ProjectTestReportWebPartDataManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestReportWebPartDataManager, self).get_queryset()

    def get(self, data_id):
        result = None
        try:
            result = self.all().get(id=data_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_report_webparts(self, reportID):
        return self.all().filter(Report=reportID)


class ProjectTestReportWebPartManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectTestReportWebPartManager, self).get_queryset()

    def get(self, id):
        result = None
        try:
            result = self.all().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_report_webparts(self, project_id):
        result = None
        try:
            result = self.all().filter(Project=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectScenesManager(ModelManager):
    '''
    classdocs
    '''

    def all(self):
        return super(ProjectScenesManager, self).get_queryset().filter(IsActive=0)


class ProjectCaseReviewManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectCaseReviewManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(ProjectCaseReviewManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.warning("case_review_id=" + str(id) + ":" + str(ex))
        return result

    def get_project_casereview(self, projectid):
        result = None
        try:
            result = self.all().filter(Project=projectid)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_version_casereview(self, version_id):
        result = None
        try:
            result = self.all().filter(Version=version_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_review(self, case_review_id):
        result = None
        try:
            result = self.all().filter(id=case_review_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ProjectCaseReviewTestcaseManager(ModelManager):
    '''
    classdocs
    '''
    use_in_migrations = True

    def all(self):
        return super(ProjectCaseReviewTestcaseManager, self).get_queryset().filter(IsActive=1)

    def get(self, id):
        result = None
        try:
            result = super(ProjectCaseReviewTestcaseManager, self).get_queryset().get(id=id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_review_cases(self, casereview_id):
        result = None
        try:
            result = self.all().filter(CaseReview=casereview_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ApiTestCollectionManager(ModelManager):
    '''
    API测试集合管理器
    '''
    use_in_migrations = True

    def all(self):
        return super(ApiTestCollectionManager, self).get_queryset().filter(IsActive=1)

    def get(self, collection_id):
        result = None
        try:
            result = super(ApiTestCollectionManager, self).get_queryset().get(id=collection_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_collections(self, project_id, parent=0):
        """获取项目的集合树"""
        result = None
        try:
            result = self.all().filter(project_id=project_id, parent=parent).order_by('sort_order', 'id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_collection_tree(self, project_id):
        """获取完整的集合树结构"""
        result = []
        try:
            # 获取根级集合
            root_collections = self.get_project_collections(project_id, 0)
            for collection in root_collections:
                collection_data = {
                    'id': collection.id,
                    'name': collection.name,
                    'is_folder': collection.is_folder,
                    'children': self._get_children_recursive(collection.id)
                }
                result.append(collection_data)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def _get_children_recursive(self, parent_id):
        """递归获取子集合"""
        children = []
        try:
            child_collections = self.all().filter(parent=parent_id).order_by('sort_order', 'id')
            for child in child_collections:
                child_data = {
                    'id': child.id,
                    'name': child.name,
                    'is_folder': child.is_folder,
                    'children': self._get_children_recursive(child.id)
                }
                children.append(child_data)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return children


class ApiTestCaseManager(ModelManager):
    '''
    API测试用例管理器
    '''
    use_in_migrations = True

    def all(self):
        return super(ApiTestCaseManager, self).get_queryset().filter(IsActive=1)

    def get(self, case_id):
        result = None
        try:
            result = super(ApiTestCaseManager, self).get_queryset().get(id=case_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_collection_cases(self, collection_id):
        """获取集合下的测试用例"""
        result = None
        try:
            result = self.all().filter(collection_id=collection_id).order_by('sort_order', 'id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_cases(self, project_id):
        """获取项目下的所有测试用例"""
        result = None
        try:
            result = self.all().filter(collection__project_id=project_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ApiTestEnvironmentManager(ModelManager):
    '''
    API测试环境管理器
    '''
    use_in_migrations = True

    def all(self):
        return super(ApiTestEnvironmentManager, self).get_queryset().filter(IsActive=1)

    def get(self, env_id):
        result = None
        try:
            result = super(ApiTestEnvironmentManager, self).get_queryset().get(id=env_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_environments(self, project_id):
        """获取项目的环境变量"""
        result = None
        try:
            result = self.all().filter(project_id=project_id).order_by('id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_global_environments(self):
        """获取全局环境变量"""
        result = None
        try:
            result = self.all().filter(is_global=True).order_by('id')
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result


class ApiTestHistoryManager(ModelManager):
    '''
    API测试历史管理器
    '''
    use_in_migrations = True

    def all(self):
        return super(ApiTestHistoryManager, self).get_queryset().filter(IsActive=1)

    def get(self, history_id):
        result = None
        try:
            result = super(ApiTestHistoryManager, self).get_queryset().get(id=history_id)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_project_history(self, project_id, limit=50):
        """获取项目的执行历史"""
        result = None
        try:
            result = self.all().filter(project_id=project_id).order_by('-executed_at')[:limit]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_case_history(self, case_id, limit=20):
        """获取测试用例的执行历史"""
        result = None
        try:
            result = self.all().filter(test_case_id=case_id).order_by('-executed_at')[:limit]
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result
