# coding=utf-8
"""
代码片段生成器

Created on 2025-09-15
@author: zhangpeng
"""

import json
from typing import Optional
from teamvision.project.models import ApiTestCase, ApiTestEnvironment
from teamvision.business.project.api_test_executor import EnvironmentProcessor


class CodeSnippetGenerator:
    """代码片段生成器"""

    def __init__(self, test_case: ApiTestCase, environment: Optional[ApiTestEnvironment] = None):
        self.test_case = test_case
        self.environment = environment
        self.processor = EnvironmentProcessor(environment)

    def generate(self, language: str) -> str:
        """生成指定语言的代码片段"""
        if language == 'curl':
            return self.generate_curl()
        elif language == 'python':
            return self.generate_python_requests()
        elif language == 'javascript':
            return self.generate_javascript_fetch()
        elif language == 'java':
            return self.generate_java_okhttp()
        elif language == 'go':
            return self.generate_go_http()
        else:
            return f"# Unsupported language: {language}"

    def generate_curl(self) -> str:
        """生成cURL命令"""
        url = self.processor.resolve_variables(self.test_case.url)
        method = self.test_case.method
        headers = self.processor.resolve_dict(self.test_case.headers)
        query_params = self.processor.resolve_dict(self.test_case.query_params)

        curl_parts = [f"curl -X {method}"]

        # 添加请求头
        for key, value in headers.items():
            curl_parts.append(f'-H "{key}: {value}"')

        # 添加查询参数
        if query_params:
            query_string = '&'.join([f"{k}={v}" for k, v in query_params.items()])
            if '?' in url:
                url += f"&{query_string}"
            else:
                url += f"?{query_string}"

        # 添加请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body = json.dumps(self.processor.resolve_dict(self.test_case.body_data), indent=2)
                curl_parts.append(f"-d '{body}'")
            elif self.test_case.body_type == 'form-data':
                for key, value in self.processor.resolve_dict(self.test_case.body_data).items():
                    curl_parts.append(f'-F "{key}={value}"')

        # 添加认证
        if self.test_case.auth_type == 'basic':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            username = auth_config.get('username', '')
            password = auth_config.get('password', '')
            curl_parts.append(f'-u "{username}:{password}"')
        elif self.test_case.auth_type == 'bearer':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            token = auth_config.get('token', '')
            curl_parts.append(f'-H "Authorization: Bearer {token}"')

        curl_parts.append(f'"{url}"')
        return ' \\\n  '.join(curl_parts)

    def generate_python_requests(self) -> str:
        """生成Python requests代码"""
        url = self.processor.resolve_variables(self.test_case.url)
        headers = self.processor.resolve_dict(self.test_case.headers)
        query_params = self.processor.resolve_dict(self.test_case.query_params)
        method = self.test_case.method.lower()

        code_lines = [
            "import requests",
            "import json",
            "",
            f'url = "{url}"'
        ]

        # 添加请求头
        if headers:
            code_lines.append(f"headers = {json.dumps(headers, indent=2)}")
        else:
            code_lines.append("headers = {}")

        # 添加查询参数
        if query_params:
            code_lines.append(f"params = {json.dumps(query_params, indent=2)}")

        # 添加请求体
        body_section = ""
        params_list = ["url"]
        
        if headers:
            params_list.append("headers=headers")
        
        if query_params:
            params_list.append("params=params")

        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body_data = self.processor.resolve_dict(self.test_case.body_data)
                code_lines.append(f"data = {json.dumps(body_data, indent=2)}")
                params_list.append("json=data")
            elif self.test_case.body_type in ['form-data', 'x-www-form-urlencoded']:
                body_data = self.processor.resolve_dict(self.test_case.body_data)
                code_lines.append(f"data = {json.dumps(body_data, indent=2)}")
                params_list.append("data=data")

        # 添加认证
        if self.test_case.auth_type == 'basic':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            username = auth_config.get('username', '')
            password = auth_config.get('password', '')
            code_lines.append(f'auth = ("{username}", "{password}")')
            params_list.append("auth=auth")
        elif self.test_case.auth_type == 'bearer':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            token = auth_config.get('token', '')
            if not headers:
                code_lines.append("headers = {}")
            code_lines.append(f'headers["Authorization"] = "Bearer {token}"')

        code_lines.extend([
            "",
            f"response = requests.{method}({', '.join(params_list)})",
            "",
            'print(f"Status Code: {response.status_code}")',
            'print(f"Response: {response.text}")'
        ])

        return '\n'.join(code_lines)

    def generate_javascript_fetch(self) -> str:
        """生成JavaScript Fetch代码"""
        url = self.processor.resolve_variables(self.test_case.url)
        headers = self.processor.resolve_dict(self.test_case.headers)
        query_params = self.processor.resolve_dict(self.test_case.query_params)
        method = self.test_case.method

        # 处理查询参数
        if query_params:
            query_string = '&'.join([f"{k}={v}" for k, v in query_params.items()])
            if '?' in url:
                url += f"&{query_string}"
            else:
                url += f"?{query_string}"

        code_lines = [
            f'const url = "{url}";',
            "",
            "const options = {",
            f'  method: "{method}",',
        ]

        # 添加请求头
        if headers or self.test_case.auth_type == 'bearer':
            code_lines.append("  headers: {")
            for key, value in headers.items():
                code_lines.append(f'    "{key}": "{value}",')
            
            # 添加Bearer认证
            if self.test_case.auth_type == 'bearer':
                auth_config = self.processor.resolve_dict(self.test_case.auth_config)
                token = auth_config.get('token', '')
                code_lines.append(f'    "Authorization": "Bearer {token}",')
            
            code_lines.append("  },")

        # 添加请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body_data = self.processor.resolve_dict(self.test_case.body_data)
                code_lines.append(f"  body: JSON.stringify({json.dumps(body_data, indent=2)}),")

        code_lines.extend([
            "};",
            "",
            "fetch(url, options)",
            "  .then(response => {",
            "    console.log('Status:', response.status);",
            "    return response.text();",
            "  })",
            "  .then(data => {",
            "    console.log('Response:', data);",
            "  })",
            "  .catch(error => {",
            "    console.error('Error:', error);",
            "  });"
        ])

        return '\n'.join(code_lines)

    def generate_java_okhttp(self) -> str:
        """生成Java OkHttp代码"""
        url = self.processor.resolve_variables(self.test_case.url)
        headers = self.processor.resolve_dict(self.test_case.headers)
        method = self.test_case.method

        code_lines = [
            "import okhttp3.*;",
            "import java.io.IOException;",
            "",
            "public class ApiTest {",
            "    public static void main(String[] args) throws IOException {",
            "        OkHttpClient client = new OkHttpClient();",
            "",
        ]

        # 构建请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body_data = json.dumps(self.processor.resolve_dict(self.test_case.body_data))
                code_lines.extend([
                    "        MediaType JSON = MediaType.get(\"application/json; charset=utf-8\");",
                    f'        RequestBody body = RequestBody.create(JSON, "{body_data}");',
                    ""
                ])

        # 构建请求
        code_lines.append("        Request.Builder requestBuilder = new Request.Builder()")
        code_lines.append(f'            .url("{url}")')

        # 添加请求头
        for key, value in headers.items():
            code_lines.append(f'            .addHeader("{key}", "{value}")')

        # 添加认证
        if self.test_case.auth_type == 'bearer':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            token = auth_config.get('token', '')
            code_lines.append(f'            .addHeader("Authorization", "Bearer {token}")')

        # 添加方法和请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            code_lines.append(f'            .{method.lower()}(body);')
        else:
            if method == 'GET':
                code_lines.append('            .get();')
            else:
                code_lines.append(f'            .{method.lower()}(RequestBody.create("", null));')

        code_lines.extend([
            "",
            "        Request request = requestBuilder.build();",
            "",
            "        try (Response response = client.newCall(request).execute()) {",
            "            System.out.println(\"Status: \" + response.code());",
            "            System.out.println(\"Response: \" + response.body().string());",
            "        }",
            "    }",
            "}"
        ])

        return '\n'.join(code_lines)

    def generate_go_http(self) -> str:
        """生成Go HTTP代码"""
        url = self.processor.resolve_variables(self.test_case.url)
        headers = self.processor.resolve_dict(self.test_case.headers)
        method = self.test_case.method

        code_lines = [
            "package main",
            "",
            "import (",
            "    \"fmt\"",
            "    \"io/ioutil\"",
            "    \"net/http\"",
            "    \"strings\"",
            ")",
            "",
            "func main() {",
        ]

        # 构建请求体
        if self.test_case.body_type != 'none' and self.test_case.body_data:
            if self.test_case.body_type == 'raw':
                body_data = json.dumps(self.processor.resolve_dict(self.test_case.body_data))
                code_lines.append(f'    payload := strings.NewReader(`{body_data}`)')
            else:
                code_lines.append('    payload := strings.NewReader("")')
        else:
            code_lines.append('    payload := strings.NewReader("")')

        code_lines.extend([
            "",
            f'    req, err := http.NewRequest("{method}", "{url}", payload)',
            "    if err != nil {",
            "        fmt.Println(err)",
            "        return",
            "    }",
            ""
        ])

        # 添加请求头
        for key, value in headers.items():
            code_lines.append(f'    req.Header.Add("{key}", "{value}")')

        # 添加认证
        if self.test_case.auth_type == 'bearer':
            auth_config = self.processor.resolve_dict(self.test_case.auth_config)
            token = auth_config.get('token', '')
            code_lines.append(f'    req.Header.Add("Authorization", "Bearer {token}")')

        code_lines.extend([
            "",
            "    res, err := http.DefaultClient.Do(req)",
            "    if err != nil {",
            "        fmt.Println(err)",
            "        return",
            "    }",
            "    defer res.Body.Close()",
            "",
            "    body, err := ioutil.ReadAll(res.Body)",
            "    if err != nil {",
            "        fmt.Println(err)",
            "        return",
            "    }",
            "",
            "    fmt.Println(\"Status:\", res.Status)",
            "    fmt.Println(\"Response:\", string(body))",
            "}"
        ])

        return '\n'.join(code_lines)
