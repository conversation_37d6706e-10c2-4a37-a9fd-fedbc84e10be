# coding=utf-8
"""
API测试执行器

Created on 2025-09-15
@author: zhangpeng
"""

import requests
import json
import time
import re
from typing import Dict, Any, Optional
from teamvision.project.models import ApiTestCase, ApiTestEnvironment
from gatesidelib.common.simplelogger import SimpleLogger


class EnvironmentProcessor:
    """环境变量处理器"""

    def __init__(self, environment: Optional[ApiTestEnvironment] = None):
        self.variables = {}
        if environment:
            self.variables.update(environment.variables)
            self.variables.update(environment.secrets)

    def resolve_variables(self, text: str) -> str:
        """解析文本中的环境变量"""
        if not isinstance(text, str):
            return text

        pattern = r'\{\{(\w+)\}\}'

        def replace_var(match):
            var_name = match.group(1)
            return str(self.variables.get(var_name, match.group(0)))

        return re.sub(pattern, replace_var, text)

    def resolve_dict(self, data: Dict) -> Dict:
        """解析字典中的环境变量"""
        if not isinstance(data, dict):
            return data

        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = self.resolve_variables(value)
            elif isinstance(value, dict):
                result[key] = self.resolve_dict(value)
            else:
                result[key] = value
        return result


class ApiTestExecutor:
    """API测试执行器"""

    def __init__(self, test_case: ApiTestCase, environment_id: Optional[int] = None, override_config: Dict = None):
        self.test_case = test_case
        self.environment = self._load_environment(environment_id)
        self.override_config = override_config or {}
        self.session = requests.Session()
        self.processor = EnvironmentProcessor(self.environment)

    def _load_environment(self, environment_id: Optional[int]) -> Optional[ApiTestEnvironment]:
        """加载环境变量"""
        if environment_id:
            try:
                return ApiTestEnvironment.objects.get(id=environment_id, IsActive=True)
            except ApiTestEnvironment.DoesNotExist:
                SimpleLogger.warning(f"Environment {environment_id} not found")
        return None

    def execute(self) -> Dict[str, Any]:
        """执行API测试"""
        try:
            # 1. 执行前置脚本
            self._execute_pre_script()

            # 2. 构建请求
            request_config = self._build_request()

            # 3. 发送请求
            start_time = time.time()
            response = self.session.request(**request_config)
            end_time = time.time()

            # 4. 处理响应
            result = self._process_response(response, end_time - start_time)

            # 5. 执行后置脚本和断言
            test_results = self._execute_post_script_and_assertions(response)
            result['test_results'] = test_results
            result['is_success'] = all(t.get('passed', False) for t in test_results) if test_results else True

            return result

        except Exception as e:
            SimpleLogger.exception(f"API test execution failed: {str(e)}")
            return {
                'is_success': False,
                'error_message': str(e),
                'status_code': None,
                'headers': {},
                'body': '',
                'response_time': 0,
                'response_size': 0,
                'test_results': []
            }

    def _execute_pre_script(self):
        """执行前置脚本"""
        if self.test_case.pre_request_script:
            try:
                # 这里可以实现JavaScript脚本执行
                # 暂时跳过
                pass
            except Exception as e:
                SimpleLogger.warning(f"Pre-request script execution failed: {str(e)}")

    def _build_request(self) -> Dict[str, Any]:
        """构建请求配置"""
        # 处理URL和路径变量
        url = self._process_url()

        # 处理请求头
        headers = self._process_headers()

        # 处理查询参数
        params = self._process_query_params()

        # 处理请求体
        data, json_data, files = self._process_body()

        # 处理认证
        auth = self._process_auth()

        config = {
            'method': self.test_case.method,
            'url': url,
            'headers': headers,
            'params': params,
            'timeout': 30
        }

        if data:
            config['data'] = data
        if json_data:
            config['json'] = json_data
        if files:
            config['files'] = files
        if auth:
            config['auth'] = auth

        return config

    def _process_url(self) -> str:
        """处理URL和路径变量"""
        url = self.processor.resolve_variables(self.test_case.url)
        
        # 处理路径变量
        for key, value in self.test_case.path_variables.items():
            resolved_value = self.processor.resolve_variables(str(value))
            url = url.replace(f"{{{key}}}", resolved_value)
        
        return url

    def _process_headers(self) -> Dict[str, str]:
        """处理请求头"""
        headers = self.processor.resolve_dict(self.test_case.headers.copy())
        
        # 应用覆盖配置
        if 'headers' in self.override_config:
            headers.update(self.override_config['headers'])
        
        return headers

    def _process_query_params(self) -> Dict[str, str]:
        """处理查询参数"""
        params = self.processor.resolve_dict(self.test_case.query_params.copy())
        
        # 应用覆盖配置
        if 'query_params' in self.override_config:
            params.update(self.override_config['query_params'])
        
        return params

    def _process_body(self) -> tuple:
        """处理请求体"""
        data = None
        json_data = None
        files = None

        if self.test_case.body_type == 'none':
            return data, json_data, files

        body_data = self.test_case.body_data.copy()
        
        # 应用覆盖配置
        if 'body_data' in self.override_config:
            body_data.update(self.override_config['body_data'])

        if self.test_case.body_type == 'raw':
            json_data = self.processor.resolve_dict(body_data)
        elif self.test_case.body_type == 'form-data':
            data = self.processor.resolve_dict(body_data)
        elif self.test_case.body_type == 'x-www-form-urlencoded':
            data = self.processor.resolve_dict(body_data)

        return data, json_data, files

    def _process_auth(self):
        """处理认证"""
        if self.test_case.auth_type == 'none':
            return None
        
        auth_config = self.processor.resolve_dict(self.test_case.auth_config.copy())
        
        if self.test_case.auth_type == 'basic':
            username = auth_config.get('username', '')
            password = auth_config.get('password', '')
            return (username, password)
        
        # 其他认证类型可以在这里扩展
        return None

    def _process_response(self, response: requests.Response, response_time: float) -> Dict[str, Any]:
        """处理响应数据"""
        try:
            response_body = response.text
            content_type = response.headers.get('content-type', '')

            # 尝试解析JSON
            if 'application/json' in content_type:
                try:
                    response_body = response.json()
                except:
                    pass

            return {
                'status_code': response.status_code,
                'status_text': response.reason,
                'headers': dict(response.headers),
                'body': response_body,
                'response_time': int(response_time * 1000),  # 转换为毫秒
                'response_size': len(response.content),
                'cookies': dict(response.cookies)
            }
        except Exception as e:
            raise Exception(f"处理响应失败: {str(e)}")

    def _execute_post_script_and_assertions(self, response: requests.Response) -> list:
        """执行后置脚本和断言"""
        test_results = []
        
        try:
            # 执行后置脚本
            if self.test_case.post_request_script:
                # 这里可以实现JavaScript脚本执行
                pass
            
            # 执行断言
            for assertion in self.test_case.test_assertions:
                result = self._execute_assertion(assertion, response)
                test_results.append(result)
                
        except Exception as e:
            SimpleLogger.warning(f"Post-script/assertion execution failed: {str(e)}")
            test_results.append({
                'name': 'Script Execution',
                'passed': False,
                'message': str(e)
            })
        
        return test_results

    def _execute_assertion(self, assertion: Dict, response: requests.Response) -> Dict:
        """执行单个断言"""
        try:
            assertion_type = assertion.get('type', 'status_code')
            expected = assertion.get('expected')
            
            if assertion_type == 'status_code':
                actual = response.status_code
                passed = actual == expected
            elif assertion_type == 'response_time':
                # 这里需要从外部传入响应时间
                actual = 0  # 临时值
                passed = actual <= expected
            else:
                passed = False
                actual = None
            
            return {
                'name': assertion.get('name', assertion_type),
                'type': assertion_type,
                'expected': expected,
                'actual': actual,
                'passed': passed,
                'message': f"Expected {expected}, got {actual}" if not passed else "Passed"
            }
        except Exception as e:
            return {
                'name': assertion.get('name', 'Unknown'),
                'passed': False,
                'message': str(e)
            }

    def get_request_snapshot(self) -> Dict:
        """获取请求快照"""
        return {
            'method': self.test_case.method,
            'url': self._process_url(),
            'headers': self._process_headers(),
            'query_params': self._process_query_params(),
            'body_type': self.test_case.body_type,
            'body_data': self.test_case.body_data,
            'auth_type': self.test_case.auth_type,
            'environment_id': self.environment.id if self.environment else None,
            'override_config': self.override_config
        }
